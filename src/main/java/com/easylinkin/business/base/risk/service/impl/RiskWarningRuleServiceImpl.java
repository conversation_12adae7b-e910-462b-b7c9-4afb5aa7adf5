package com.easylinkin.business.base.risk.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.easylinkin.business.base.risk.constant.NoticeWayEnum;
import com.easylinkin.business.base.risk.constant.ThresholdTypeEnum;
import com.easylinkin.business.base.risk.constant.TriggerTypeEnum;
import com.easylinkin.business.base.risk.entity.*;
import com.easylinkin.business.base.risk.mapper.RiskWarningRecordNoticeRefUserMapper;
import com.easylinkin.business.base.risk.mapper.RiskWarningRuleMapper;
import com.easylinkin.business.base.risk.mapper.RiskWarningTeamMapper;
import com.easylinkin.business.base.risk.service.*;
import com.easylinkin.business.base.risk.util.RiskWarningRuleUtils;
import com.easylinkin.business.base.risk.vo.*;
import com.easylinkin.business.base.ruleengine.util.ExpressionUtil;
import com.easylinkin.business.sys.entity.SysDictItem;
import com.easylinkin.business.sys.service.SysAppService;
import com.easylinkin.business.sys.service.SysDictService;
import com.easylinkin.common.core.web.exception.ApiException;
import com.easylinkin.common.util.CronUtils;
import com.easylinkin.iot.device.cache.CacheHelper;
import com.easylinkin.iot.device.entity.*;
import com.easylinkin.iot.device.mapper.DeviceUnitMapper;
import com.easylinkin.iot.device.mapper.DeviceUnitPropertyMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 风险预警机制
 * <AUTHOR>
 * @date 2025/02/28
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class RiskWarningRuleServiceImpl implements RiskWarningRuleService, ApplicationContextAware {

    private final RiskWarningRuleMapper mapper;
    private final RiskThresholdService thresholdService;
    private final RiskThresholdExpressionService expressionService;
    private final RiskLevelService riskLevelService;
    private final RiskWarningRecordService warningRecordService;
    private final DeviceUnitPropertyMapper deviceUnitPropertyMapper;
    private final CacheHelper cacheHelper;
    private final RiskWarningTeamMapper teamMapper;
    private final RiskWarningRecordNoticeRefUserMapper recordNoticeRefUserMapper;
    private final DeviceUnitMapper deviceUnitMapper;
    private final SysAppService sysAppService;
    private static HashMap<Long,String> appId2CodeMap = new HashMap<>();
    private final SysDictService sysDictService;
    private final RiskWarningRuleUtils riskWarningRuleUtils;


    @Override
    public BaseMapper<RiskWarningRule> getMapper() {
        return mapper;
    }

    @Override
    public Class<RiskWarningRule> getEntityClass() {
        return RiskWarningRule.class;
    }

    @Override
    public IPage<RiskWarningRuleVo> selectPage(RiskWarningRuleQueryRequestVo requestVo) {

        Map<Long, String> levelNames = riskLevelService.listByAppId(requestVo.getAppId())
            .stream().collect(Collectors.toMap(RiskLevelVo::getId, RiskLevelVo::getLevelName));

        // 1. 执行分页查询
        Page<RiskWarningRule> pageResult = mapper.selectPage(requestVo.toIPage(), requestVo.getQueryWrapper());

        // 2. 转换并填充统计数据
        return pageResult.convert(entity -> {
            RiskWarningRuleVo vo = new RiskWarningRuleVo();
            BeanUtils.copyProperties(entity, vo);
            // 获取阈值数量
            LambdaQueryWrapper<RiskThreshold> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(RiskThreshold::getRiskWarningRuleId, entity.getId());
            vo.setThresholdCount(thresholdService.count(wrapper));
            // 风险等级名称
            vo.setLevelName(levelNames.get(entity.getRiskLevelId()));
            return vo;
        });
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public RiskWarningRule add(RiskWarningRuleRequestVo vo) {
        // 检查编码唯一性
        checkCodeUnique(vo.getRuleCode(), null);

        // 检查通知相关字段
        checkNotifyFields(vo);

        // 保存主表数据
        RiskWarningRule rule = create(vo.toEntity());

        // 保存阈值和表达式
        saveThresholdsAndExpressions(rule.getId(), vo.getThresholds());

        return rule;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public RiskWarningRule modify(Long id, RiskWarningRuleRequestVo vo) {
        // 检查编码唯一性
        checkCodeUnique(vo.getRuleCode(), id);

        // 检查通知相关字段
        checkNotifyFields(vo);

        // 更新主表数据
        RiskWarningRule rule = updateById(id, vo.toEntity());

        // 删除旧的阈值和表达式数据
        deleteOldThresholdsAndExpressions(id);

        // 保存新的阈值和表达式
        saveThresholdsAndExpressions(id, vo.getThresholds());

        return rule;
    }

    private void checkCodeUnique(String code, Long excludeId) {
        LambdaQueryWrapper<RiskWarningRule> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(RiskWarningRule::getRuleCode, code)
               .ne(excludeId != null, RiskWarningRule::getId, excludeId);
        if (mapper.selectCount(wrapper) > 0) {
            throw new ApiException("风险预警机制编码已存在");
        }
    }

    private void checkNotifyFields(RiskWarningRuleRequestVo vo) {
        // 不校验，改为设置为空数组
        // if (CollectionUtils.isEmpty(vo.getWarningTeamIds()) && CollectionUtils.isEmpty(vo.getSpecifiedUserIds())) {
        //     throw new ApiException("风险报警团队和指定用户不能同时为空");
        // }
        if (vo.getWarningTeamIds()==null) {
            vo.setWarningTeamIds(new ArrayList<>());
        }
        if (vo.getSpecifiedUserIds()==null) {
            vo.setSpecifiedUserIds(new ArrayList<>());
        }
    }

    private void deleteOldThresholdsAndExpressions(Long ruleId) {
        // 删除旧的表达式
        LambdaQueryWrapper<RiskThresholdExpression> expressionWrapper = new LambdaQueryWrapper<>();
        expressionWrapper.eq(RiskThresholdExpression::getRiskThresholdId, ruleId);
        expressionService.remove(expressionWrapper);

        // 删除旧的阈值
        LambdaQueryWrapper<RiskThreshold> thresholdWrapper = new LambdaQueryWrapper<>();
        thresholdWrapper.eq(RiskThreshold::getRiskWarningRuleId, ruleId);
        thresholdService.remove(thresholdWrapper);
    }

    private void saveThresholdsAndExpressions(Long ruleId, List<RiskThresholdVo> thresholds) {
        for (int i = 0; i < thresholds.size(); i++) {
            RiskThresholdVo thresholdVo = thresholds.get(i);

            // 保存阈值
            RiskThreshold threshold = new RiskThreshold();
            BeanUtils.copyProperties(thresholdVo, threshold);
            threshold.setId(null);
            threshold.setRiskWarningRuleId(ruleId);
            thresholdService.create(threshold);

            // 如果是静态阈值&&触发类型是属性，保存表达式
            if (threshold.getType() == ThresholdTypeEnum.STATIC.getValue()
                && threshold.getTriggerType() == TriggerTypeEnum.PROPERTY.getValue()
                && !CollectionUtils.isEmpty(thresholdVo.getExpressions())) {
                for (RiskThresholdExpression expression : thresholdVo.getExpressions()) {
                    expression.setId(null);
                    expression.setRiskThresholdId(threshold.getId());
                    expressionService.create(expression);
                }
            }
        }
    }

    @Override
    public RiskWarningRuleDetailVo getDetail(Long id) {
        // 获取主表数据
        RiskWarningRule rule = get(id);
        if (rule == null) {
            throw new ApiException("风险预警机制不存在");
        }

        // 转换为VO
        RiskWarningRuleDetailVo vo = new RiskWarningRuleDetailVo();
        BeanUtils.copyProperties(rule, vo);
        vo.setNotifyTypes(Arrays.asList(rule.getNoticeWay().split(",")));

        // 获取阈值列表
        LambdaQueryWrapper<RiskThreshold> thresholdWrapper = new LambdaQueryWrapper<>();
        thresholdWrapper.eq(RiskThreshold::getRiskWarningRuleId, id);
        List<RiskThreshold> thresholds = thresholdService.selectList(thresholdWrapper);

        // 转换阈值数据
        List<RiskThresholdDetailVo> thresholdVos = thresholds.stream().map(threshold -> {
            RiskThresholdDetailVo thresholdVo = new RiskThresholdDetailVo();
            BeanUtils.copyProperties(threshold, thresholdVo);

            // 获取表达式列表
            if (threshold.getType() == ThresholdTypeEnum.STATIC.getValue()) { // 静态阈值
                LambdaQueryWrapper<RiskThresholdExpression> expressionWrapper = new LambdaQueryWrapper<>();
                expressionWrapper.eq(RiskThresholdExpression::getRiskThresholdId, threshold.getId())
                    .orderByAsc(RiskThresholdExpression::getSortNo);
                List<RiskThresholdExpression> expressions = expressionService.selectList(expressionWrapper);
                thresholdVo.setExpressions(expressions);
            }
            List<String> deviceIds = threshold.getDeviceIds();
            if (!CollectionUtils.isEmpty(deviceIds)) {
                List<Device> deviceList = new ArrayList<>();
                for (String deviceId : deviceIds) {
//                    Device oneByDeviceCode = deviceService.findOneByDeviceCode(deviceId);
                    Device oneByDeviceCode = cacheHelper.getDeviceByCode(deviceId);
                    deviceList.add(oneByDeviceCode);
                }
                thresholdVo.setDeviceList(deviceList);
            }
            return thresholdVo;
        }).collect(Collectors.toList());

        vo.setThresholds(thresholdVos);

        return vo;
    }

    @Override
    public void triggerRiskWarning(FlowPushData flowPushData,Long appId) {
        Device device = flowPushData.getDevice();
        if (device == null || StringUtils.isEmpty(device.getCode())) {
            log.warn("triggerRiskWarning - 设备参数为空");
            return;
        }
        Object data = flowPushData.getData();
        List<DeviceStatus> deviceStatusList = flowPushData.getDeviceStatusList();
        device.setSourceJson(JSONObject.toJSONString(data));

        // 1. 查询所有可能的预警机制
        LambdaQueryWrapper<RiskWarningRule> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(RiskWarningRule::getAppId, appId);
        List<RiskWarningRule> rules = selectList(wrapper);
        if (CollectionUtils.isEmpty(rules)) {
            return;
        }

        // 2. 遍历预警机制，检查是否满足触发条件
        Date now = new Date();
        for (RiskWarningRule rule : rules) {
            // 2.1 检查cron表达式
            if (!checkCronExpression(rule.getTriggerRule(), now)) {
                continue;
            }

            // 2.2 检查阈值条件
            if (!checkThresholds(rule, device, deviceStatusList)) {
                riskWarningRecordDetailList.remove();
                continue;
            }

            // 3. 生成预警记录
            RiskWarningRecord riskWarningRecord = warningRecordService.generateWarning(rule, device);

            // 4. 发送通知
            sendNotifications(rule, riskWarningRecord);
        }
    }

    @Override
    public void triggerRiskWarning(Device device, String eventType) {
        if (device == null || StringUtils.isEmpty(device.getCode())) {
            log.warn("triggerRiskWarning - 设备参数为空");
            return;
        }
        // 1. 查询所有可能的预警机制
        LambdaQueryWrapper<RiskWarningRule> wrapper = new LambdaQueryWrapper<>();
//        wrapper.eq(RiskWarningRule::getId,"1896837087772704770");
        List<RiskWarningRule> rules = selectList(wrapper);
        if (CollectionUtils.isEmpty(rules)) {
            return;
        }

        // 2. 遍历预警机制，检查是否满足触发条件
        Date now = new Date();
        for (RiskWarningRule rule : rules) {
            // 2.1 检查cron表达式
            if (!checkCronExpression(rule.getTriggerRule(), now)) {
                continue;
            }

            // 2.2 检查阈值条件
            if (!checkThresholds(rule.getId(), device, eventType)) {
                riskWarningRecordDetailList.remove();
                continue;
            }

            // 3. 生成预警记录
            RiskWarningRecord riskWarningRecord = warningRecordService.generateWarning(rule, device);

            // 4. 发送通知
            sendNotifications(rule, riskWarningRecord);
        }
    }

    /**
     * 执行风险预判-定时任务执行
     * @param threshold
     */
    @Override
    public void execute(RiskThreshold threshold) {
        List<String> deviceIds = threshold.getDeviceIds();
        if (CollectionUtil.isEmpty(deviceIds)){
            //deviceIds循环
            deviceIds.forEach(deviceId -> {
                //产生告警记录
                Device device = cacheHelper.getDeviceByCode(deviceId);
                if (device == null) {
                    log.error("deviceflowData-handleDeviceEvent 查找设备为空,参数是:{}", deviceId);
                    return;
                }
                //模型触发告警
                generateModelWarning(threshold, device);
            });
        }
    }

    /**
     * 模型触发告警
     * @param threshold
     * @param device
     */
    private void generateModelWarning(RiskThreshold threshold, Device device) {
        //现有模型都为 1， 后续再修改
        if (Integer.valueOf(1).equals(threshold.getRiskModel())){
            //查询对应的规则
            RiskWarningRule warningRule = get(threshold.getRiskWarningRuleId());
            if (null != warningRule){
                RiskWarningRuleUtils.LevelScore levelScore = riskWarningRuleUtils.predict(warningRule.getAppId());
                //这里先构造一个对象，后面再优化
                addDetail(device, threshold,null);
                // 3. 生成预警记录
                warningRule.setRiskLevelId(levelScore.levelId());
                RiskWarningRecord riskWarningRecord = warningRecordService.generateWarning(warningRule, device);

                // 4. 发送通知
                sendNotifications(warningRule, riskWarningRecord);
            }
        }
    }

    /**
     * 在离线事件触发预警
     * @param ruleId
     * @param device
     * @param eventType  事件类别。在线：“online_state”；离线：“offline_state”； 规则触发：“rule_trigger"
     * @return
     */
    private boolean checkThresholds(Long ruleId, Device device, String eventType) {
        riskWarningRecordDetailList.remove();
        // 查询规则关联的阈值
        LambdaQueryWrapper<RiskThreshold> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(RiskThreshold::getRiskWarningRuleId, ruleId);
        List<RiskThreshold> thresholds = thresholdService.selectList(wrapper);

        if (CollectionUtils.isEmpty(thresholds)) {
            return false;
        }


        // 检查每个阈值,如果配置了多个阈值，取并集
        boolean result = true;
        for (RiskThreshold threshold : thresholds) {
            // 检查设备是否在监控范围内,如果该设备不在这个某个配置中，则这整个风险规则中的判断都不成立
            if (!threshold.getDeviceIds().contains(device.getCode())) {
                riskWarningRecordDetailList.remove();
                return false;
            }

            // 动态模型阈值暂时返回true
            if (threshold.getType() == ThresholdTypeEnum.DYNAMIC.getValue()) {
                log.info("TODO: 动态模型阈值待实现, 暂时返回true");
                //这里先构造一个对象，后面再优化
                addDetail(device, threshold,null);
            }

            // 静态阈值检查
            if (threshold.getType() == ThresholdTypeEnum.STATIC.getValue()) {
                // 如果是常规规则 只需要判断 设备在线离线是否符合
                if (TriggerTypeEnum.ONLINE_STATUS.getValue().equals(threshold.getTriggerType())) {
                    //data只会是
                    if (threshold.getCommonRuleItem()!=null && threshold.getCommonRuleItem()==0 && eventType.equals("offline_state")){
//                        result = result && true;
                        addDetail(device, threshold,0);
                    }else if (threshold.getCommonRuleItem()!=null && threshold.getCommonRuleItem()==1 && eventType.equals("online_state")){
                        addDetail(device, threshold,1);
                    }else {
                        return false;
                    }
                } else {
                    //如果不是在离线，这里直接判断异常，因为在离线跟流水属性就不在一包流水中
                   return false;
                }
            }
        }
        return result;
    }

    /**
     * 检查cron表达式是否满足当前时间
     */
    private boolean checkCronExpression(String cronExpression, Date now) {
        try {
            return CronUtils.isMatchCron(cronExpression, now);
//            CronExpression cron = new CronExpression(cronExpression);
//            // 检查上一次满足条件的时间是否在1分钟内
//            Date lastTime = cron.getTimeBefore(now);
//            if (lastTime == null) {
//                return false;
//            }
//            return now.getTime() - lastTime.getTime() <= 60 * 1000;
        } catch (Exception e) {
            log.error("解析cron表达式失败: {}", cronExpression, e);
            return false;
        }
    }

    /**
     * 检查阈值条件
     */
    private boolean checkThresholds(RiskWarningRule rule, Device device, List<DeviceStatus> deviceStatusList) {
        riskWarningRecordDetailList.remove();
        // 查询规则关联的阈值
        LambdaQueryWrapper<RiskThreshold> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(RiskThreshold::getRiskWarningRuleId, rule.getId());
        List<RiskThreshold> thresholds = thresholdService.selectList(wrapper);

        if (CollectionUtils.isEmpty(thresholds)) {
            return false;
        }


        // 检查每个阈值,如果配置了多个阈值，取并集
        boolean result = true;
        for (RiskThreshold threshold : thresholds) {
            // 检查设备是否在监控范围内,如果该设备不在这个某个配置中，则这整个风险规则中的判断都不成立
            if (!threshold.getDeviceIds().contains(device.getCode())) {
                riskWarningRecordDetailList.remove();
                return false;
            }

            // 动态模型阈值暂时返回true
            if (threshold.getType() == ThresholdTypeEnum.DYNAMIC.getValue()) {
                log.info("TODO: 动态模型阈值待实现, 暂时返回true");
//                return true;
//                result = result && true;
                //获取到当前的应用id
                //这里先构造一个对象，后面再优化
                threshold.setRiskModelDictCode(appId2CodeMap.get(rule.getAppId()));
                addDetail(device, threshold,null);
            }

            // 静态阈值检查
            if (threshold.getType() == ThresholdTypeEnum.STATIC.getValue()) {
                // 如果是常规规则 只需要判断 设备在线离线是否符合
                if (TriggerTypeEnum.ONLINE_STATUS.getValue().equals(threshold.getTriggerType())) {
                    // 如果是上下线推送,数据因为格式不一样，不走这里，直接将这个判断成不成立
                    return false;
                } else {
                    // 查询阈值表达式
                    LambdaQueryWrapper<RiskThresholdExpression> expressionWrapper = new LambdaQueryWrapper<>();
                    expressionWrapper.eq(RiskThresholdExpression::getRiskThresholdId, threshold.getId())
                        .orderByAsc(RiskThresholdExpression::getSortNo);
                    List<RiskThresholdExpressionVo> expressions = expressionService.selectVoList(expressionWrapper);

                    if (!CollectionUtils.isEmpty(expressions)) {
                        result = result && checkExpressions(expressions, deviceStatusList);
                    }
                }
            }
        }
        return result;
    }

    private void addDetail(Device device, RiskThreshold threshold,Integer ruleItem) {
        RiskWarningRecordDetail detail = new RiskWarningRecordDetail();
        detail.setThresholdTitle(threshold.getTitle());
        detail.setThresholdId(threshold.getId());
        detail.setThresholdType(threshold.getType());
        detail.setDeviceUnitId(threshold.getDeviceUnitId());
        detail.setDeviceCode(device.getCode());
        DeviceUnit deviceUnit = deviceUnitMapper.selectById(threshold.getDeviceUnitId());
        if (deviceUnit != null){
            detail.setDeviceUnitCode(deviceUnit.getCode());
        }
        //如果ruleItem不为空，则为在离线，将express设置成在线或离线
        if (ruleItem != null){
            if (ruleItem==0){
                detail.setExpression("设备离线触发");
            }else {
                detail.setExpression("设备在线触发");
            }
        }
        if (StringUtils.isNotBlank(threshold.getRiskModelDictCode())){
            for (SysDictItem s : sysDictService.selectListByCode(threshold.getRiskModelDictCode())) {
                if (threshold.getRiskModel().toString().equals(s.getItemValue())){
                    detail.setRiskModelName(s.getItemText());
                }
            }
        }
        List<RiskWarningRecordDetail> riskWarningRecordDetails = riskWarningRecordDetailList.get();
        if (riskWarningRecordDetails == null){
            detail.setSort(1);
            riskWarningRecordDetails = new ArrayList<>();
            riskWarningRecordDetails.add(detail);
            riskWarningRecordDetailList.set(riskWarningRecordDetails);
        }else {
            long count = riskWarningRecordDetails.stream().filter(d -> d.getThresholdId().equals(threshold.getId())).count();
            detail.setSort((int) count + 1);
            riskWarningRecordDetails.add(detail);
            riskWarningRecordDetailList.set(riskWarningRecordDetails);
        }
    }

    /**
     * 检查表达式是否满足
     */
    private boolean checkExpressions(List<RiskThresholdExpressionVo> expressions, List<DeviceStatus> deviceStatusList) {
        boolean result = true;

        for (RiskThresholdExpressionVo expression : expressions) {
            // 查找对应的设备状态
            DeviceStatus status = deviceStatusList.stream()
                .filter(s -> s.getProp().equals(expression.getProp()))
                .findFirst()
                .orElse(null);

            if (status == null) {
                continue;
            }
            DeviceUnitProperty deviceUnitProperty = deviceUnitPropertyMapper.selectById(
                expression.getDeviceAttributeId());
            if (deviceUnitProperty == null) {
                continue;
            }
            expression.setDeviceUnitProperty(deviceUnitProperty);
            // 检查表达式
            boolean currentResult = checkSingleExpression(expression, status.getValue());

            //如果触发了，需要将这个规则放到详情中
            if (currentResult){
                addDetail(status, expression);
            }

            // 处理逻辑运算符
            if (StringUtils.isEmpty(expression.getLogicCode()) || "&&".equals(expression.getLogicCode())) {
                result = result && currentResult;
            } else if ("||".equals(expression.getLogicCode())) {
                result = result || currentResult;
            }
        }

        return result;
    }

    private void addDetail(DeviceStatus status, RiskThresholdExpressionVo expression) {
        Long riskThresholdId = expression.getRiskThresholdId();
        RiskThreshold threshold = thresholdService.selectById(riskThresholdId);
        RiskWarningRecordDetail detail = new RiskWarningRecordDetail();
        detail.setThresholdTitle(threshold.getTitle());
        detail.setThresholdId(threshold.getId());
        detail.setThresholdType(threshold.getType());
        detail.setDeviceUnitId(threshold.getDeviceUnitId());
        detail.setDeviceCode(status.getDeviceCode());
        DeviceUnit deviceUnit = deviceUnitMapper.selectById(threshold.getDeviceUnitId());
        if (deviceUnit != null){
            detail.setDeviceUnitCode(deviceUnit.getCode());
        }
        detail.setPropName(status.getProp());
        detail.setCalculateSign(expression.getCalculateSign());
        detail.setExpression(expressThreadLocal.get());
        List<RiskWarningRecordDetail> riskWarningRecordDetails = riskWarningRecordDetailList.get();
        if (riskWarningRecordDetails == null){
            detail.setSort(1);
            riskWarningRecordDetails = new ArrayList<>();
            riskWarningRecordDetails.add(detail);
            riskWarningRecordDetailList.set(riskWarningRecordDetails);
        }else {
            long count = riskWarningRecordDetails.stream().filter(d -> d.getThresholdId().equals(threshold.getId())).count();
            detail.setSort((int) count + 1);
            riskWarningRecordDetails.add(detail);
            riskWarningRecordDetailList.set(riskWarningRecordDetails);
        }
    }

    /**
     * 检查单个表达式是否满足条件
     * @param expression 表达式
     * @param actualValue 实际值
     * @return 是否满足条件
     */
    private boolean checkSingleExpression(RiskThresholdExpression expression, String actualValue) {
        if (StringUtils.isEmpty(actualValue)) {
            return false;
        }

        String expectedValue = expression.getValue();
        String calculateSign = expression.getCalculateSign();
        String dataType = expression.getDeviceUnitProperty().getUnit();

//        // 对于枚举和布尔类型，需要从specs中获取实际值
//        if ("enum".equals(dataType) || "bool".equals(dataType)) {
//            String specs = expression.getDeviceUnitProperty().getSpecs();
//            if (StringUtils.isNotEmpty(specs)) {
//                try {
//                    JSONObject jsonObj = JSONObject.parseObject(specs);
//                    Object obj = jsonObj.get(actualValue);
//                    if (obj != null) {
//                        actualValue = obj.toString();
//                    }
//                } catch (Exception e) {
//                    log.error("解析设备属性specs失败: {}", e.getMessage());
//                    return false;
//                }
//            }
//        }

        try {
            return ExpressionUtil.checkExpressionMeet(dataType, actualValue, calculateSign, expectedValue);
        } catch (Exception e) {
            log.error("表达式检查失败: actualValue={}, expectedValue={}, dataType={}, calculateSign={}",
                actualValue, expectedValue, dataType, calculateSign, e);
            return false;
        }
    }

    /**
     * 发送通知
     */
    private void sendNotifications(RiskWarningRule rule, RiskWarningRecord riskWarningRecord) {
        if (StringUtils.isEmpty(rule.getNoticeWay())) {
            return;
        }

        // 通知日期
        Date date = new Date();

        List<String> notifyTypes = Arrays.asList(rule.getNoticeWay().split(","));

        // 获取通知对象
        List<String> teamIds = rule.getWarningTeamIds();
        // 用户对象，冗余校验
        List<String> userIds = Optional.ofNullable(rule.getSpecifiedUserIds()).orElse(new ArrayList<>());

        // TODO: 调用消息服务发送通知
        if (notifyTypes.contains(String.valueOf(NoticeWayEnum.MESSAGE.getValue()))) {
            log.info("TODO: 发送消息通知");
        }

        if (notifyTypes.contains(String.valueOf(NoticeWayEnum.SMS.getValue()))) {
            log.info("TODO: 发送短信通知");
        }

        // 临时，直接往记录关联通知表里插入记录
        try {
            // 获取团队
            List<RiskWarningTeam> teams;
            if (CollectionUtil.isNotEmpty(teamIds)){
                teams = teamMapper.selectList(Wrappers.<RiskWarningTeam>lambdaQuery().in(CollectionUtil.isNotEmpty(teamIds),RiskWarningTeam::getId, teamIds));
            } else {
                teams = new ArrayList<>();
            }
            // 从RiskWarningTeam.members的jsonArray获取用户
            List<String> teamUserIds = teams.stream().map(RiskWarningTeam::getMembers).flatMap(members -> Arrays.stream(members.split(","))).toList();
            // 合并用户
            userIds.addAll(teamUserIds);
            // 去重
            userIds = userIds.stream().distinct().toList();

            for (String userId : userIds) {
                RiskWarningRecordNoticeRefUser refUser = new RiskWarningRecordNoticeRefUser();
                refUser.setRecordId(riskWarningRecord.getId());
                refUser.setUserId(Long.valueOf(userId));
                refUser.setNoticeTime(date);
                recordNoticeRefUserMapper.insert(refUser);
            }
        } catch (Exception e){
            log.error("插入记录关联通知表失败", e);
        }
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        sysAppService.selectList(new QueryWrapper<>()).stream().forEach(sysApp -> appId2CodeMap.put(sysApp.getId(), sysApp.getCode()+".risk_model_id_"));
    }
}
