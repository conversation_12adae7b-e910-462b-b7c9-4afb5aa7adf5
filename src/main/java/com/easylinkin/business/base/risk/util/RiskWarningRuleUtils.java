package com.easylinkin.business.base.risk.util;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.easylinkin.business.app.constant.AppConstant;
import com.easylinkin.business.base.risk.entity.RiskLevel;
import com.easylinkin.business.base.risk.mapper.RiskLevelMapper;
import com.easylinkin.business.sys.entity.SysApp;
import com.easylinkin.business.sys.service.SysAppService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Description 风险预判工具类
 * <AUTHOR> yuan<PERSON>
 * @Date 2025/5/30 11:45
 */
@Component
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class RiskWarningRuleUtils {

    private final SysAppService sysAppService;
    private final RiskLevelMapper riskLevelMapper;

    /**
     * 再建一个对外方法。根据appId判断使用哪个方法进行判断
     */
    public LevelScore predict(Long appId) {
        //根据appId查询对应的appCode。根据appCode判断
        SysApp sysApp = sysAppService.get(appId);
        if (null != sysApp){
            String appCode = sysApp.getCode();
            //燃气
            if (AppConstant.GAS_APP_CODE.equals(appCode)) {
                gasPredict(appId);
            //桥梁
            } else if (AppConstant.BRIDGE_APP_CODE.equals(appCode)) {
                bridgePredict(appId);
            //供水
            } else if (AppConstant.WATER_APP_CODE.equals(appCode)) {
                waterPredict(appId);
            //排水
            } else if (AppConstant.DRAINAGE_APP_CODE.equals(appCode)) {
                drainagePredict(appId);
            //井盖
            } else if (AppConstant.MANHOLE_APP_CODE.equals(appCode)) {
                manholePredict(appId);
            //电梯
            } else if (AppConstant.ELEVATOR_APP_CODE.equals(appCode)) {
                elevatorPredict(appId);
            }
        }

    }

    //新建6个方法。分别为燃气，桥梁，供水，排水，井盖，电梯的预判方法

    /**
     * 燃气风险研判
     */
    private LevelScore gasPredict(Long appId) {
    }

    /**
     * 桥梁风险研判
     */
    private LevelScore bridgePredict(Long appId) {
    }

    /**
     * 供水风险研判
     */
    private LevelScore waterPredict(Long appId) {
        return new LevelScore(getLevelByScore(1D,appId), 1D);
    }

    /**
     * 排水风险研判
     */
    private LevelScore drainagePredict(Long appId) {
        return new LevelScore(getLevelByScore(1D,appId), 1D);
    }

    /**
     * 井盖风险研判
     */
    private LevelScore manholePredict(Long appId) {
        return new LevelScore(getLevelByScore(1D,appId), 1D);
    }

    /**
     * 电梯风险研判
     */
    private LevelScore elevatorPredict(Long appId) {
        return new LevelScore(getLevelByScore(1D,appId), 1D);
    }

    /**
     * 新建一个 record 属性为等级和分数
     * @param levelId 4低风险 3中风险(一般风险) 2高风险 1 重大风险 。 得到的是等级对应的id
     * @param score
     */
    public record LevelScore(Long levelId, Double score) {
    }

    /**
     * 通过分数获取level
     * 0-4 分为低风险，5-6为一版风险，7-8 分为高风险，9-10 分为重大风险
     */
    private Long getLevelByScore(Double score,Long appId) {
        String level;
        if (score < 5) {
            level = "4";
        } else if (score < 7) {
            level = "3";
        } else if (score < 9) {
            level = "2";
        } else {
            level = "1";
        }
        RiskLevel riskLevel = riskLevelMapper.selectOne(new LambdaQueryWrapper<RiskLevel>()
                .eq(RiskLevel::getAppId, appId)
                .eq(RiskLevel::getLevelDictValue, level));
        return riskLevel == null?null:riskLevel.getId();
    }
}
