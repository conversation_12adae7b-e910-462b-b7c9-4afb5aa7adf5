package com.easylinkin.business.map.service.impl;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.easylinkin.business.app.constant.AppConstant;
import com.easylinkin.business.base.device.constant.FacilityRefModuleEnum;
import com.easylinkin.business.base.device.entity.BaseFacilityInfo;
import com.easylinkin.business.base.device.mapper.BaseFacilityInfoMapper;
import com.easylinkin.business.base.inspect.mapper.InspectMaintenanceTaskMapper;
import com.easylinkin.business.base.inspect.mapper.InspectTaskMapper;
import com.easylinkin.business.base.warning.service.MonitorWarningEventStatisticsService;
import com.easylinkin.business.base.warning.service.MonitorWarningRecordService;
import com.easylinkin.business.base.warning.vo.MonitorWarningRecordQueryRequestVo;
import com.easylinkin.business.base.warning.vo.MonitorWarningRecordVo;
import com.easylinkin.business.base.warning.vo.statics.MonitorWarningEventStatisticsQueryVo;
import com.easylinkin.business.base.warning.vo.statics.MonitorWarningEventStatisticsVo;
import com.easylinkin.business.bridge.service.BridgeBasicInfoService;
import com.easylinkin.business.bridge.vo.BridgeBasicInfoQueryRequestVo;
import com.easylinkin.business.bridge.vo.BridgeBasicInfoVo;
import com.easylinkin.business.map.service.BridgeMapService;
import com.easylinkin.business.screen.vo.BridgeInspectMaintenanceVo;
import com.easylinkin.common.core.mybatis.entity.BaseEntity;
import com.easylinkin.common.flow.FlowDataUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 桥梁一张图监测大屏服务实现类
 * <AUTHOR>
 * @date 2024/07/11
 */
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class BridgeMapServiceImpl implements BridgeMapService {

    private final MonitorWarningEventStatisticsService monitorWarningEventStatisticsService;
    private final BridgeBasicInfoService bridgeBasicInfoService;
    private final BaseFacilityInfoMapper baseFacilityInfoMapper;
    private final InspectTaskMapper inspectTaskMapper;
    private final InspectMaintenanceTaskMapper inspectMaintenanceTaskMapper;
    private final MonitorWarningRecordService monitorWarningRecordService;

    /**
     * 获取桥梁统计数据
     * 数据来源：辅助支持子系统-桥梁决策管理-桥梁监测综合统计分析
     *
     * @return 统计数据，包含桥梁总数、报警事件数
     */
    @Override
    public MonitorWarningEventStatisticsVo.WarningTotal getStatistics() {
        MonitorWarningEventStatisticsQueryVo requestVo = new MonitorWarningEventStatisticsQueryVo();
        requestVo.setRefModule(1);
        requestVo.setAppId(FlowDataUtil.getAppIdByCode(AppConstant.BRIDGE_APP_CODE));
        MonitorWarningEventStatisticsVo.WarningTotal total = monitorWarningEventStatisticsService.getTotal(requestVo);
        return total;
    }

    /**
     * 获取桥梁列表
     * @return 桥梁列表数据
     */
    @Override
    public List<BridgeBasicInfoVo> getBridgeList() {
        BridgeBasicInfoQueryRequestVo requestVo = new BridgeBasicInfoQueryRequestVo();
        requestVo.setSize(-1);
        requestVo.setCurrent(1);
        IPage<BridgeBasicInfoVo> infoVoIPage = bridgeBasicInfoService.pageVo(requestVo.toIPage(), requestVo.getQueryWrapper());
        return infoVoIPage.getRecords();
    }

    /**
     * 获取桥梁详情
     * 数据来源：桥梁信息管理
     * @param id 桥梁ID
     * @return 桥梁详情数据
     */
    @Override
    public BridgeBasicInfoVo getBridgeDetail(Long id) {
        // 查询桥梁基本信息
        BridgeBasicInfoVo basicInfoVo = bridgeBasicInfoService.getVoById(id);
        return basicInfoVo;
    }

    /**
     * 获取桥梁巡检养护数据
     * @param id 桥梁ID
     * @return 巡检养护数据
     */
    @Override
    public List<BridgeInspectMaintenanceVo> getBridgeMaintenance(Long id) {
        //查询桥梁对应的设备/设施
        Long facilityId = bridgeBasicInfoService.get(id).getFacilityId();
        //查询桥梁对应的设备/设施
        List<Long> objIds = baseFacilityInfoMapper.selectList(Wrappers.lambdaQuery(BaseFacilityInfo.class)
                        .eq(BaseFacilityInfo::getRefModule, FacilityRefModuleEnum.BRIDGE.getModuleId())
                        .eq(BaseFacilityInfo::getRefId, id))
                .stream().map(BaseEntity::getId).collect(Collectors.toList());
        objIds.add(facilityId);

        // 获取近30天的日期范围
        Calendar calendar = Calendar.getInstance();
        Date endDate = DateUtil.endOfDay(calendar.getTime());
        calendar.add(Calendar.DAY_OF_MONTH, -29);
        Date startDate = DateUtil.beginOfDay(calendar.getTime());

        // 1. 使用SQL查询 - 直接查询objIds范围内30天的巡检数据统计
        List<Map<String, Object>> inspectStats = inspectTaskMapper.selectInspectStatsByTime(objIds, startDate, endDate);

        // 2. 使用SQL查询 - 直接查询objIds范围内30天的养护数据统计
        List<Map<String, Object>> maintenanceStats = inspectMaintenanceTaskMapper.selectMaintenanceStatsByTime(objIds, startDate, endDate);

        // 3. 将查询结果转换为Map方便查找
        Map<String, Long> inspectDateCountMap = new HashMap<>();
        for (Map<String, Object> stat : inspectStats) {
            String statDate = (String) stat.get("statDate");
            Long count = ((Number) stat.get("count")).longValue();
            inspectDateCountMap.put(statDate, count);
        }

        Map<String, Long> maintenanceDateCountMap = new HashMap<>();
        for (Map<String, Object> stat : maintenanceStats) {
            String statDate = (String) stat.get("statDate");
            Long count = ((Number) stat.get("count")).longValue();
            maintenanceDateCountMap.put(statDate, count);
        }

        // 4. 构建30天趋势数据
        List<BridgeInspectMaintenanceVo> trendList = new ArrayList<>();
        LocalDate today = LocalDate.now();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

        // 5. 构建每一天的数据
        for (int i = 29; i >= 0; i--) {
            LocalDate date = today.minusDays(i);
            Date javaDate = Date.from(date.atStartOfDay(ZoneId.systemDefault()).toInstant());
            String dateStr = sdf.format(javaDate);

            BridgeInspectMaintenanceVo vo = new BridgeInspectMaintenanceVo();
            vo.setDate(javaDate);
            vo.setInspectCount(inspectDateCountMap.getOrDefault(dateStr, 0L).intValue());
            vo.setMaintenanceCount(maintenanceDateCountMap.getOrDefault(dateStr, 0L).intValue());

            trendList.add(vo);
        }

        return trendList;
    }

    /**
     * 获取最新预警数据
     * @return 预警数据
     */
    @Override
    public List<MonitorWarningRecordVo> getLatestWarnings(Long id) {
        // 查询桥梁的预警记录
        MonitorWarningRecordQueryRequestVo requestVo = new MonitorWarningRecordQueryRequestVo();
        requestVo.setRefId(id);
        requestVo.setCurrent(1);
        requestVo.setSize(5);
        Page<MonitorWarningRecordVo> page = monitorWarningRecordService.findPage(requestVo);
        return page.getRecords();
    }
}
